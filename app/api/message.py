import logging
from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, File, Form, HTTPException, Request, UploadFile, status

from constants.operation_ids import operation_ids
from dependencies import ConversationMessageServiceDep, OwnerOnlyPermissionDep
from exceptions import (
    ConversationDataInconsistencyError,
    EntityNotFoundError,
    MaximumDocumentsNumberExceeded,
    MaximumDocumentsSizeExceeded,
)
from schemas import BaseMessageSerializer, CombinedMessageSerializer, MessageSerializer, Option


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/messages')


@router.post(
    '',
    operation_id=operation_ids.message.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create(
    request: Request,
    message_service: ConversationMessageServiceDep,
    conversation_id: Annotated[UUID, Form()],
    content: Annotated[str, Form()] = '',
    selected_option: Annotated[Option | None, Form(description='Selected option as an object')] = None,
    files: Annotated[list[UploadFile] | None, File(description='Optional files to attach to the message')] = None,
) -> CombinedMessageSerializer:
    """
    Create a new message for an existing conversation.

    Parameters:
        request: Request object containing the Authorization header
        message_service: Injected message service dependency
        conversation_id: UUID of the conversation to add the message to
        content: Text content of the message (optional)
        selected_option: The selected option
        files: List of files to attach to the message (optional)

    Returns:
        CombinedMessageSerializer containing both the created user message
        and a system message with expected entity information

    Raises:
        HTTPException:
            - 404 if conversation not found
            - 400 for validation errors or file size/count limits
            - 500 for server errors

    Notes:
        - If files are provided without content, message_type will be set to FILE
        - Files are stored before the response is returned and processed then in a parallel task
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')
    try:
        return await message_service.create(
            token=auth_token,
            conversation_id=conversation_id,
            content=content,
            selected_option=selected_option,
            files=files,
        )
    except EntityNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e)) from e
    except (MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded, ValueError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)) from e
    except ConversationDataInconsistencyError as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)) from e
    except Exception as e:  # pragma: no cover
        logger.exception('Error creating message')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to create message. {e}',
        ) from e


@router.get(
    '/{message_id}',
    operation_id=operation_ids.message.GET,
    response_model=MessageSerializer,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get(
    message_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> BaseMessageSerializer:
    """
    Get a conversation message by its ID.
    """
    try:
        return await message_service.get(message_id)
    except EntityNotFoundError as e:  # pragma: no cover
        # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:  # pragma: no cover
        logger.exception('Error retrieving message')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to retrieve message. {e}',
        ) from e

from datetime import date, datetime
import logging
from typing import Sequence

from constants.extracted_data import ConversationState, MissingDataStatus, RequiredField
from constants.message import (
    SystemReplyType,
)
from schemas import AggregatedData, ClientNameOption, ConfirmedData, DatePickerOption, LDMFCountryOption, Option
from schemas.extracted_data import MissingDataResponse


__all__ = ['SystemMessageGenerationService']


logger = logging.getLogger(__name__)

GENERATED_MESSAGE = tuple[str, SystemReplyType]


class SystemMessageGenerationService:
    """
    Service for generating system messages based on extracted data.

    This service consolidates the system message generation logic from Azure Durable Functions
    into the main FastAPI application for better maintainability and immediate response generation.
    """

    def generate_system_message(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        missing_data_response: MissingDataResponse,
    ) -> GENERATED_MESSAGE:
        """
        Generate a user-friendly system message based on aggregated and confirmed data.

        Args:
            aggregated_data: The aggregated data from all sources
            confirmed_data: The confirmed data from previous user selections
            missing_data_response: Missing data response containing conversation state

        Returns:
            System reply depending on aggregated and confirmed data
        """
        if missing_data_response.status == MissingDataStatus.CLIENT_NAME_NOT_IN_API:
            reply_type = SystemReplyType.CLIENT_NOT_FOUND
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_system_message.__name__)
            return reply_type.message_text.format(client_name=aggregated_data.client_name[0]), reply_type

        # Early return for initial state
        if aggregated_data.all_fields_none or not aggregated_data.client_name:
            return SystemReplyType.NEED_INFO_INITIAL.message_text, SystemReplyType.NEED_INFO_INITIAL

        # Define field processing order and their handlers
        field_handlers = [
            self.generate_for_client_name,
            self.generate_for_ldmf_country,
            self.generate_for_date_intervals,
            self.generate_for_objective_and_scope,
            self.generate_for_outcomes,
        ]

        # Process fields in order, returning first incomplete field's message
        for handler in field_handlers:
            message = handler(aggregated_data, confirmed_data)
            if message:
                return message

        if confirmed_data.last_confirmed_field == RequiredField.LDMF_COUNTRY:
            system_message = SystemReplyType.LDMF_COUNTRY_CONFIRMED.message_text.format(
                ldmf_country=confirmed_data.ldmf_country
            )
            return system_message, SystemReplyType.LDMF_COUNTRY_CONFIRMED

        return SystemReplyType.CONFIRMED_FIELDS_READY.message_text, SystemReplyType.CONFIRMED_FIELDS_READY

    def generate_for_client_name(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle client name field processing."""
        if confirmed_data.client_name:
            return None

        if len(aggregated_data.client_name) == 1:
            return SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
                client_name=aggregated_data.client_name[0]
            ), SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
        else:
            return (
                SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS.message_text,
                SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS,
            )

    def generate_for_ldmf_country(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle LDMF country field processing."""
        if confirmed_data.ldmf_country:
            return None

        if not aggregated_data.ldmf_country:
            return SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text, SystemReplyType.EXTRACTED_LDMF_NOT_VALID

        if len(aggregated_data.ldmf_country) == 1:
            return SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION.message_text.format(
                ldmf_country=aggregated_data.ldmf_country[0]
            ), SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
        else:
            return (
                SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS.message_text,
                SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS,
            )

    def generate_for_date_intervals(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle date intervals field processing."""
        if not aggregated_data.date_intervals and not confirmed_data.date_intervals:
            return (
                SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING.message_text,
                SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING,
            )

        if confirmed_data.date_intervals:
            return None

        if not aggregated_data.is_date_unambiguous_and_complete:
            return SystemReplyType.DATES_AMBIGUOUS.message_text, SystemReplyType.DATES_AMBIGUOUS

        return None

    def generate_for_objective_and_scope(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle objective and scope field processing."""
        if not aggregated_data.objective_and_scope:
            return (
                SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING.message_text,
                SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING,
            )

        if confirmed_data.objective_and_scope:
            return None

        return SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE.message_text.format(
            objective_and_scope=aggregated_data.objective_and_scope
        ), SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE

    def generate_for_outcomes(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle outcomes field processing."""
        if not aggregated_data.outcomes:
            return (
                SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING.message_text,
                SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING,
            )

        if confirmed_data.outcomes:
            return None

        return SystemReplyType.OUTCOMES_AGGREGATED_QUESTION.message_text.format(
            outcomes=aggregated_data.outcomes
        ), SystemReplyType.OUTCOMES_AGGREGATED_QUESTION

    def generate_options(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData | None = None,
        conversation_state: ConversationState | None = None,
    ) -> Sequence[Option]:
        """
        Generate user selection options based on aggregated data and confirmed data.

        Args:
            aggregated_data: The aggregated data from all sources
            confirmed_data: The confirmed data from previous user selections

        Returns:
            List of option dictionaries for user selection
        """
        options = []
        if aggregated_data.all_fields_none:
            return options

        # Client Names - highest priority (only if not confirmed)
        if (
            aggregated_data.client_name
            and len(aggregated_data.client_name) > 1
            and (not confirmed_data or not confirmed_data.client_name)
            and conversation_state == ConversationState.COLLECTING_CLIENT_NAME
        ):
            options.extend([ClientNameOption(client_name=name) for name in aggregated_data.client_name])
            return options

        # Lead Member Firm Countries - second priority (only if not confirmed)
        if (
            aggregated_data.ldmf_country
            and len(aggregated_data.ldmf_country) > 1
            and (not confirmed_data or not confirmed_data.ldmf_country)
            and conversation_state == ConversationState.COLLECTING_COUNTRY
        ):
            options.extend([LDMFCountryOption(ldmf_country=country) for country in aggregated_data.ldmf_country])
            return options

        # Engagement Dates - third priority (only if not confirmed or ambiguous)
        if (
            not aggregated_data.is_date_unambiguous_and_complete
            and (not confirmed_data or not confirmed_data.date_intervals)
            and conversation_state == ConversationState.COLLECTING_DATES
        ):
            for start_date_str, end_date_str in aggregated_data.date_intervals:
                if start_date_str or end_date_str:
                    if self._are_dates_ambiguous(start_date_str, end_date_str):
                        start_date = self._parse_date_string(start_date_str) if start_date_str else None
                        end_date = self._parse_date_string(end_date_str) if end_date_str else None
                        options.append(DatePickerOption(start_date=start_date, end_date=end_date))
                        return options

        return options

    @staticmethod
    def _parse_date_string(date_str: str) -> date | None:
        """
        Parse date string to date object.

        Args:
            date_str: Date string in ISO format (YYYY-MM-DD)

        Returns:
            Date object or None if parsing fails
        """
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            logger.warning('Failed to parse date string "%s": %s', date_str, e)
            return None

    @staticmethod
    def _are_dates_ambiguous(start_date: str | None, end_date: str | None) -> bool:
        """
        Check if dates are ambiguous (day <= 12 could be misinterpreted) or incomplete.

        Args:
            start_date: Start date string in ISO format (YYYY-MM-DD)
            end_date: End date string in ISO format (YYYY-MM-DD)

        Returns:
            True if any date has day <= 12 (ambiguous) or dates are incomplete, False otherwise
        """
        if not start_date or not end_date:
            return True

        processed_start_date = date.fromisoformat(start_date)
        processed_end_date = date.fromisoformat(end_date)

        return processed_start_date.day <= 12 or processed_end_date.day <= 12

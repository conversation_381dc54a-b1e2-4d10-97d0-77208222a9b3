import datetime
import logging
from typing import List, Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.durable_functions import ProcessingStatus
from constants.extracted_data import ConfirmedDataFields, ConversationState, MissingDataStatus, RequiredField
from constants.message import (
    ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND,
    ALL_REQUIRED_FIELDS_EXTRACTED_DOCS,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from exceptions import ConversationDataInconsistencyError, EntityNotFoundError
from models import QualConversation
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentDbRepository,
    ProcessingMessageRepository,
)
from schemas import (
    AggregatedData,
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    ConfirmedD<PERSON>,
    ConversationData,
    ConversationMessageProcessingResult,
    DatePickerOption,
    DocumentCreationRequest,
    LDMFCountryOption,
    MessageContent,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from schemas.conversation_message.option import KXDashTaskOption
from schemas.extracted_data import FieldHandlerResponse
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_processor import ConversationMessageProcessor
from services.proactive_chat import ProactiveChatService
from services.suggestions import SuggestedPromptsGenerator
from services.system_message_generation import SystemMessageGenerationService
from services.system_message_reply_type import SystemMessageReplyTypeGenerator


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    # Define the expected empty LDMF country option for clarity
    _EMPTY_LDMF_COUNTRY_OPTION = [LDMFCountryOption(type=OptionType.LDMF_COUNTRY, ldmf_country='')]

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        document_db_repository: DocumentDbRepository,
        processing_message_repository: ProcessingMessageRepository,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        system_message_generation_service: SystemMessageGenerationService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.document_db_repository = document_db_repository
        self.processing_message_repository = processing_message_repository
        self.kx_dash_service = kx_dash_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.system_message_service = system_message_generation_service

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """
        await self._ensure_previous_message_processed(conversation_id)

        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )

        user_message = await self.create_message(user_message_to_persist)
        user_message = cast(UserMessageSerializer, user_message)

        conversation_data = None
        intention = None
        confirmed_data = None
        system_reply_type = None

        if selected_option:
            match selected_option.type:
                case OptionType.CLIENT_NAME:
                    system_message = await self._handle_client_name_selection(selected_option, conversation_id, token)
                case OptionType.LDMF_COUNTRY:
                    system_message = await self._handle_country_selection(selected_option, conversation_id)
                case OptionType.DATES:
                    system_message = await self._handle_dates_selection(selected_option, conversation_id)
                case OptionType.KX_DASH_TASK:
                    system_message = await self.kx_dash_service.on_select(selected_option, conversation_id, token=token)
                case _:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
        else:
            # Get conversation message history for message processor & suggested prompts tracking.
            conversation_message_history = await self.conversation_message_repository.get_combined_history(
                conversation_id
            )

            # Process the user message to get the system reply and intention
            message_processor = ConversationMessageProcessor(
                conversation_id=conversation_id,
                user_message=user_message,
                conversation_message_history=conversation_message_history,
                intent_classifier_service=self.intent_classifier_service,
                extracted_data_service=self.extracted_data_service,
                conversation_repository=self.conversation_repository,
                date_validator_service=self.date_validator_service,
                document_service=self.document_service,
                token=token,
            )
            message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
            system_reply_type = message_processing_result.system_reply_type
            intention = message_processing_result.intention
            user_message_to_persist.intention = intention

            # Get aggregated extracted and confirmed data after message processing.
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

            # Get suggested prompts
            suggested_prompts = await self.get_suggested_prompts(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                files=files,
                intention=intention,
                current_reply_type=system_reply_type,
                called_from='ConversationMessageService.create.no_options_flow',
            )

            # Update the user message with the determined intention
            await self.update_message_fields(user_message.id, {'Intention': intention})

            if intention == ConversationMessageIntention.DASH_DISCARD:
                dash_discard_message = await self._get_dash_discard_response(
                    user_message, message_processing_result, suggested_prompts
                )
                return dash_discard_message

            system_message = None
            should_generate_system_message = not (
                (content and intention == ConversationMessageIntention.EXTRACTION) or files
            )
            if should_generate_system_message:
                system_message = await self._get_system_message_with_result(
                    user_message_to_persist, message_processing_result, suggested_prompts
                )
                if not system_message.content:
                    system_message = None

        if not conversation_data:
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

        # Append enriched message to system message
        if system_message:
            confirmed_data = conversation_data.confirmed_data
            is_dash_task = isinstance(selected_option, KXDashTaskOption)
            proactive_chat_service = ProactiveChatService(
                conversation_data=conversation_data,
                system_reply_type=system_message.system_reply_type,
                is_dash_task=is_dash_task,
            )

            if is_dash_task:
                await self.autoconfirm_data_from_kxdash(conversation_data, conversation_id, token)

            # Get proactive message content
            enriched_message_content = proactive_chat_service.get_enriched_system_message()
            missing_data_response = None
            # Get proactive options
            if (
                enriched_message_content
                and not system_message.options
                and system_message.system_reply_type
                not in [
                    SystemReplyType.UNDEFINED,
                    SystemReplyType.BRIEF_DESCRIPTION,
                    SystemReplyType.EXAMPLE,
                    SystemReplyType.CLIENT_NOT_FOUND,
                    SystemReplyType.DASH_TASK_SELECTED_TEMPLATE,
                ]
            ):
                missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                    conversation_id=conversation_id,
                    token=token,
                    confirmed_data=confirmed_data,
                )
                missing_eng_dates = missing_data_response.next_expected_field == RequiredField.ENGAGEMENT_DATES
                proactive_options = self._convert_to_option_objects(
                    missing_data_response.options, missing_data_response.conversation_state
                )
                changed_ldmf_option = self.extracted_data_service.catch_invalid_ldmf_option(
                    missing_data_response, missing_data_response.conversation_state, system_reply_type
                )
                status_is_missing = missing_data_response.status == MissingDataStatus.MISSING_DATA
                is_proactive_options_available = proactive_options and len(proactive_options) > 1
                is_ldmf_suggestion_used = SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM == content

                if changed_ldmf_option and not is_proactive_options_available and not is_ldmf_suggestion_used:
                    # The message from missing_data_response might contain a generic prompt that needs to be cleared
                    # if the system_message.content already contains a specific LDMF question.
                    ldmf_search_text = 'Could you tell me who the Lead Deloitte Member Firm is for this engagement?'
                    if missing_data_response.message and ldmf_search_text in system_message.content:
                        missing_data_response.message = ''
                    if status_is_missing:
                        missing_data_response.message = SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
                    system_message.content = f'{system_message.content}\n\n{missing_data_response.message}'
                    system_message.options = [changed_ldmf_option]
                elif missing_eng_dates:
                    system_message.content = enriched_message_content
                    system_message.options = proactive_options
                elif is_ldmf_suggestion_used and changed_ldmf_option:
                    system_message.content = system_message.content
                    system_message.options = [changed_ldmf_option]
                else:
                    system_message.content = f'{system_message.content}\n\n{enriched_message_content}'
                    system_message.options = proactive_options
            else:
                # custom proactive messages for ConversationState.DATA_COMPLETE
                if system_message.system_reply_type in [
                    SystemReplyType.CLIENT_CREATION_CONFIRMED,
                    SystemReplyType.DATES_CONFIRMED,
                    SystemReplyType.LDMF_COUNTRY_CONFIRMED,
                    SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED,
                    SystemReplyType.OUTCOMES_CONFIRMED,
                ]:
                    conversation_data = await self._fetch_conversation_data(conversation_id, token)
                    conversation: QualConversation = conversation_data.conversation

                    # Clear last_confirmed_field to prevent showing confirmation messages repeatedly
                    if system_message.system_reply_type in [
                        SystemReplyType.CLIENT_CREATION_CONFIRMED,
                        SystemReplyType.DATES_CONFIRMED,
                        SystemReplyType.LDMF_COUNTRY_CONFIRMED,
                        SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED,
                        SystemReplyType.OUTCOMES_CONFIRMED,
                    ]:
                        await self._clear_last_confirmed_field(conversation_id)

                    if str(conversation.State) == ConversationState.DATA_COMPLETE.value:
                        # same logic as in ConversationMessageProcessor._handle_data_complete_response
                        reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                        system_message.content = f'{system_message.content} {reply_type.message_text}'
                        system_message.system_reply_type = reply_type
                        system_message.suggested_prompts = [
                            SuggestedUserPrompt.NO_CREATE_MY_QUAL.value,
                        ]

            # Append countries when client name is confirmend, and there are more than 1 contries that are unconfirmed.
            # NOTE: this part should probably be refactored using _convert_to_option_objects method.
            # But the problem is that get_missing_required_data_prompts doesnt return all required data.
            # It detects only missing data, that wasnt exctracted at all yet.
            # But when user specified more than 1 country - it needs to be confirmed.
            # This state isnt checked by get_missing_required_data_prompts, but we need still need to return countries in options after user confirmed client name.
            conversation_collects_country = (
                missing_data_response.conversation_state == ConversationState.COLLECTING_COUNTRY
                if missing_data_response
                else False
            )
            client_selected = selected_option and selected_option.type == OptionType.CLIENT_NAME
            if client_selected and not conversation_collects_country:
                # may modify system_message.options
                await self._handle_ldmf_country_options_after_client_name_confirmation(
                    system_message, proactive_chat_service, conversation_data
                )

            # Add suggested prompts when original system message content was None or empty
            if not system_message.suggested_prompts:
                # method possibly modifies system_message.suggested_prompts and system_message.system_reply_type
                await self._generate_and_set_suggested_prompts(
                    system_message, conversation_id, user_message, files, conversation_data
                )

        # Prepare & return response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.create_message(system_message)) if system_message else None,
        )

        # Handle unified queue messaging for files and/or text content
        if files or content and intention == ConversationMessageIntention.EXTRACTION:
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files or [],
                message_id=response.user.id,
            )

            # Use unified approach: send text content for extraction along with files
            text_for_extraction = content if intention == ConversationMessageIntention.EXTRACTION else None
            document_responses = await self.document_service.create_combined_message(document_data, text_for_extraction)

            if document_responses:
                response.files = document_responses

        if settings.append_collected_data_to_message_response:
            response.collected_data = conversation_data.aggregated_data.model_dump()

        return response

    async def autoconfirm_data_from_kxdash(
        self, conversation_data: ConversationData, conversation_id: UUID, token: str
    ):
        aggregated_data = conversation_data.aggregated_data
        confirmed_data = conversation_data.confirmed_data
        to_update, updated_confirm_data = await self._update_fields_from_aggregated_data(
            aggregated_data, confirmed_data, token, True
        )

        if to_update:
            logger.info(
                'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s',
                conversation_id,
            )
            next_state = updated_confirm_data.get_current_conversation_state()
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id,
                confirmed_data=updated_confirm_data,
                state=next_state,
            )

    async def _generate_and_set_suggested_prompts(
        self,
        system_message: MessageValidator,
        conversation_id: UUID,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        conversation_data: ConversationData,
    ):
        system_reply_type_generator = SystemMessageReplyTypeGenerator(
            conversation_data=conversation_data,
        )
        reply_type = system_reply_type_generator.generate_system_reply_type()
        if reply_type:
            suggested_prompts = await self.get_suggested_prompts(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                files=files,
                intention=ConversationMessageIntention.EXTRACTION,
                current_reply_type=reply_type,
                called_from='ConversationMessageService.create.empty_system_message',
            )
            system_message.system_reply_type = reply_type
            system_message.suggested_prompts = [i.value for i in suggested_prompts]

    async def _handle_ldmf_country_options_after_client_name_confirmation(
        self,
        system_message: MessageValidator,
        proactive_chat_service: ProactiveChatService,
        conversation_data: ConversationData,
    ) -> None:
        """
        Handles setting LDMF country options when client name is confirmed and
        there are multiple unconfirmed LDMF countries.
        """
        confirmed_data = conversation_data.confirmed_data
        aggregated_data = conversation_data.aggregated_data
        should_set_ldmf_country_options = (
            confirmed_data.client_name and not confirmed_data.ldmf_country and len(aggregated_data.ldmf_country) > 1
        )

        if should_set_ldmf_country_options:
            if (next_field := proactive_chat_service.next_required_field) != RequiredField.LDMF_COUNTRY:
                raise RuntimeError(
                    'User has confirmed client name and specified more than 1 LDMF country, so the next required field '
                    f'is expected to be {RequiredField.LDMF_COUNTRY}, got {next_field} instead. Please validate the logic.'
                )

            if system_message.options and system_message.options != self._EMPTY_LDMF_COUNTRY_OPTION:
                raise RuntimeError(
                    f'System message options were previously modified, and contain: {system_message.options}. Please validate the logic.'
                )

            system_message.options = aggregated_data.ldmf_countries_as_options

    async def get_suggested_prompts(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        intention: ConversationMessageIntention,
        current_reply_type: SystemReplyType,
        called_from: str,
    ) -> list[SuggestedUserPrompt]:
        dash_task_activity_id: int | None = await self.conversation_repository.get_dash_task_activity_id(
            conversation_id
        )

        # Generate suggested replies for the user
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            conversation_message_history=conversation_data.conversation_message_history,
            user_message=user_message,
            intention=intention,
            files=files,
            dash_task_activity_id=dash_task_activity_id,
            current_reply_type=current_reply_type,
            called_from=called_from,
        )
        return await suggested_prompts_generator.run()

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:  # pragma: no cover
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID, token: str) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation
            token: The authentication token

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)
            last_message = await self.conversation_message_repository.get_last(public_id)

            # Return immediately if last message is not from user
            if last_message.role != MessageRole.USER:
                return last_message

            logger.debug('Last message is from user, generating system response for conversation %s', public_id)
            return await self._generate_system_response_for_user_message(public_id, last_message, token)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def _generate_system_response_for_user_message(
        self, conversation_id: UUID, last_message: BaseMessageSerializer, token: str
    ) -> BaseMessageSerializer:
        """
        Generate a system response for the last user message.

        Args:
            conversation_id: The conversation ID
            last_message: The last user message
            token: The authentication token

        Returns:
            Generated system message

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            # Fetch all required data in parallel where possible
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

            # Generate system message content and options
            message_content = await self._generate_message_content_and_options(
                conversation_id, conversation_data, last_message, token
            )

            # Create and return the system message
            system_message = await self.create_message(
                message_content.to_message_validator(conversation_id, last_message)
            )
            logger.info('Generated system message %s for conversation %s', system_message.id, conversation_id)
            return system_message

        except Exception as e:
            logger.warning('Failed to generate system message for conversation %s: %s', conversation_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    async def update_message_fields(self, message_id: UUID, fields_to_update: dict) -> None:
        """
        Update specific fields of a conversation message.

        Args:
            message_id: The ID of the message to update.
            fields_to_update: A dictionary where keys are field names and values are the new values.
        """
        try:
            logger.debug('Updating message %s with fields: %s', message_id, fields_to_update)
            await self.conversation_message_repository.update_fields(message_id, fields_to_update)
        except Exception as e:
            logger.error('Error updating message %s: %s', message_id, e)
            raise

    def _convert_to_option_objects(
        self, raw_options: List | None, conversation_state: ConversationState
    ) -> List[Option]:
        """Convert raw options to proper option objects based on conversation state."""
        if not raw_options and conversation_state == ConversationState.COLLECTING_DATES:
            raw_options = [(None, None)]
        elif not raw_options or not conversation_state:
            return []

        if conversation_state == ConversationState.COLLECTING_CLIENT_NAME:
            return [ClientNameOption(client_name=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_COUNTRY:
            return [LDMFCountryOption(ldmf_country=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_DATES:
            return [DatePickerOption(start_date=start_date, end_date=end_date) for start_date, end_date in raw_options]
        return []

    async def _get_system_message_with_result(
        self,
        message_data: MessageValidator,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> MessageValidator:
        """
        Create a system message based on the message processing result.
        """
        options = []
        # Check if we have options from the processing result
        if 'options' in processing_result.data and processing_result.data['options']:
            # Check conversation state to determine option type
            conversation_state = processing_result.data.get('conversation_state')
            if conversation_state:
                options = self._convert_to_option_objects(processing_result.data['options'], conversation_state)

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=processing_result.system_reply,
            system_reply_type=processing_result.system_reply_type,
            options=options,
            suggested_prompts=[i.value for i in suggested_prompts],
        )

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve message owner ID')
            raise

    async def _handle_country_selection(
        self, selected_option: LDMFCountryOption, conversation_id: UUID
    ) -> MessageValidator:
        try:
            logger.debug(f'Handling selection {selected_option.ldmf_country} for conversation {conversation_id}')

            # Update confirmed data with the selected country
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='ldmf_country',
                field_value=selected_option.ldmf_country,
                state=ConversationState.COLLECTING_DATES,
            )

            # Create confirmation message
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=selected_option.ldmf_country)
            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling country selection: %s', e)
            raise

    async def _handle_dates_selection(
        self, selected_option: DatePickerOption, conversation_id: UUID
    ) -> MessageValidator:
        """
        Handle dates selection from options.

        Args:
            selected_option: The selected dates option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling dates selection: %s for conversation: %s', selected_option, conversation_id)
            start_date = selected_option.start_date.isoformat() if selected_option.start_date else None
            end_date = selected_option.end_date.isoformat() if selected_option.end_date else None

            # Create confirmation message
            reply_type = SystemReplyType.DATES_CONFIRMED
            confirmation_message = reply_type.message_text
            options = []

            if start_date and end_date:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_OBJECTIVE,  # Move to next state
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_DATES,  # Stay in the same state
                )

                reply_type = SystemReplyType.DATES_ONE_DATE
                confirmation_message = reply_type.message_text
                options = [selected_option]

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                options=options,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling dates selection: %s', e)
            raise

    async def _handle_client_name_selection(
        self, selected_option: ClientNameOption, conversation_id: UUID, token: str
    ) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID
            token: The user's token

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id
            )

            missing_fields: FieldHandlerResponse = await self.extracted_data_service.single_value_validation(
                field=RequiredField.CLIENT_INFO,
                field_value=selected_option.client_name,
                token=token,
            )

            move_to_next_field = not missing_fields.needs_confirmation

            if move_to_next_field:
                # Update confirmed data with the selected client name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name='client_name',
                    field_value=selected_option.client_name,
                    state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
                )

                # Create confirmation message
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confirmation_message = reply_type.message_text.format(client_name=selected_option.client_name)

                confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
                if confirmed_data.required_fields_except_client_name_are_complete:
                    reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                    confirmation_message = ' '.join((confirmation_message, reply_type.message_text))

                return MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=confirmation_message,
                    selected_option=selected_option,
                    system_reply_type=reply_type,
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name='proposed_client_name',
                    field_value=selected_option.client_name,
                    state=ConversationState.COLLECTING_CLIENT_NAME,  # Move to next state
                )
                suggested_prompts = [SuggestedUserPrompt.YES, SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME]
                return MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=missing_fields.system_message or '',
                    selected_option=selected_option,
                    system_reply_type=missing_fields.system_reply_type,
                    options=[],
                    suggested_prompts=[str(s) for s in suggested_prompts],
                )

        except Exception as e:  # pragma: no cover
            logger.error('Error handling client name selection: %s', e)
            raise

    async def _handle_confirmed_data_update(self, public_id: UUID, aggregated_data: AggregatedData, token: str) -> None:
        """
        Handle confirmed data logic based on aggregated data state.

        Args:
            public_id: The conversation ID
            aggregated_data: The aggregated data from all sources
        """
        try:
            # Fetch confirmed data from existing conversation
            confirmed_data = await self.conversation_repository.get_confirmed_data(public_id)

            # Check if we need to update confirmed data
            should_update = False

            # If confirmed data is empty and aggregated has all required fields with single values
            if confirmed_data.is_empty and aggregated_data.is_complete:
                logger.info('Confirmed data is empty and aggregated data is complete for conversation %s', public_id)
                # Apply all fields from aggregated except client_name
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            elif confirmed_data.is_empty and not aggregated_data.is_complete:
                logger.info(
                    'Confirmed data is empty and aggregated data is not complete for conversation %s', public_id
                )
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            # If confirmed data has some fields - update from aggregated
            elif not confirmed_data.is_empty and not confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data has some fields, updating from aggregated for conversation %s', public_id)

                # Update ldmf_country if not confirmed and available in aggregated
                if (
                    not confirmed_data.ldmf_country
                    and aggregated_data.ldmf_country
                    and len(aggregated_data.ldmf_country) == 1
                ):
                    verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                        aggregated_data.ldmf_country[0], token
                    )
                    if verified_countries and len(verified_countries) == 1:
                        confirmed_data.ldmf_country = verified_countries[0]
                        should_update = True

                # Update date_intervals if not confirmed and available in aggregated
                if (
                    not confirmed_data.date_intervals
                    and aggregated_data.date_intervals
                    and aggregated_data.date_intervals_original
                    and len(aggregated_data.date_intervals) == 1
                ):
                    start_date, end_date = aggregated_data.date_intervals[0]
                    start_date_original, end_date_original = aggregated_data.date_intervals_original[0]
                    if await self._is_date_unambiguous_and_complete(
                        start_date=start_date,
                        end_date=end_date,
                        start_date_original=start_date_original,
                        end_date_original=end_date_original,
                    ):
                        confirmed_data.date_intervals = (start_date, end_date)
                        should_update = True

                # Update objective_and_scope if not confirmed and available in aggregated
                if not confirmed_data.objective_and_scope and aggregated_data.objective_and_scope:
                    confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
                    should_update = True

                # Update outcomes if not confirmed and available in aggregated
                if not confirmed_data.outcomes and aggregated_data.outcomes:
                    confirmed_data.outcomes = aggregated_data.outcomes
                    should_update = True

            # If confirmed data has all fields - do nothing
            elif confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data is complete, no update needed for conversation %s', public_id)
                return

            # Save to database and set state to collecting client name if confirmed data was updated
            if should_update:
                logger.info(
                    'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s', public_id
                )
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=public_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        except Exception:  # pragma: no cover
            logger.exception('Error handling confirmed data update for conversation %s', public_id)
            raise

    async def _update_fields_from_aggregated_data(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str, is_dash: bool = False
    ) -> tuple[bool, ConfirmedData]:
        should_update = False

        if aggregated_data.is_client_name_complete and is_dash:
            confirmed_data.client_name = aggregated_data.client_name[0]
            should_update = True

        if aggregated_data.ldmf_country and len(aggregated_data.ldmf_country) == 1:
            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                aggregated_data.ldmf_country[0], token
            )
            if verified_countries and len(verified_countries) == 1:
                confirmed_data.ldmf_country = verified_countries[0]
                should_update = True

        if (
            aggregated_data.date_intervals
            and aggregated_data.date_intervals_original
            and len(aggregated_data.date_intervals) == 1
        ):
            start_date, end_date = aggregated_data.date_intervals[0]
            start_date_original, end_date_original = aggregated_data.date_intervals_original[0]
            if await self._is_date_unambiguous_and_complete(
                start_date=start_date,
                end_date=end_date,
                start_date_original=start_date_original,
                end_date_original=end_date_original,
            ):
                confirmed_data.date_intervals = (start_date, end_date)
                should_update = True

        if aggregated_data.objective_and_scope:
            confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
            should_update = True

        if aggregated_data.outcomes:
            confirmed_data.outcomes = aggregated_data.outcomes
            should_update = True

        return should_update, confirmed_data

    async def _is_date_unambiguous_and_complete(
        self,
        start_date: str | None,
        end_date: str | None,
        start_date_original: str | None,
        end_date_original: str | None,
    ) -> bool:
        """Check if the date is unambiguous and complete."""
        if not start_date or not end_date or not start_date_original or not end_date_original:
            return False

        processed_start_date = datetime.date.fromisoformat(start_date)
        processed_end_date = datetime.date.fromisoformat(end_date)

        for date, date_original in (
            (processed_start_date, start_date_original),
            (processed_end_date, end_date_original),
        ):
            if (
                date
                and date.day <= 12
                and not (
                    await self.date_validator_service.date_is_text(user_message=date_original, date=date.isoformat())
                )
            ):
                return False

        return True

    async def _get_dash_discard_response(
        self,
        user_message: UserMessageSerializer,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> CombinedMessageSerializer:
        message = MessageValidator(
            conversation_id=user_message.conversation_id,
            role=MessageRole.SYSTEM,
            type=user_message.type,
            content=processing_result.system_reply,
            suggested_prompts=[i.value for i in suggested_prompts],
            system_reply_type=processing_result.system_reply_type,
        )

        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.create_message(message)),
        )

        return response

    async def _fetch_conversation_data(self, conversation_id: UUID, token: str) -> ConversationData:
        """
        Fetch all required conversation data efficiently.

        Args:
            conversation_id: The conversation ID
            token: The authentication token

        Returns:
            ConversationData containing all required data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        # Get aggregated data first as it's needed for confirmed data update
        aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

        # Handle confirmed data logic
        await self._handle_confirmed_data_update(conversation_id, aggregated_data, token)

        # Fetch remaining data - these could potentially be done in parallel
        conversation_message_history = await self.conversation_message_repository.get_combined_history(conversation_id)
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        conversation = await self.conversation_repository.get(conversation_id)

        if not conversation:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        return ConversationData(
            conversation_message_history=conversation_message_history,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )

    async def _generate_message_content_and_options(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate system message content and options based on conversation state.

        Args:
            conversation_id: The conversation ID
            conversation_data: Pre-fetched conversation data
            last_message: The last user message
            token: The authentication token

        Returns:
            MessageContent with formatted content, reply type, and options
        """
        # Check if we should use client name single confirmation message
        if conversation_data.should_use_client_confirmation:
            return await self._generate_client_confirmation_content(conversation_data, last_message, token)
        else:
            return await self._generate_standard_system_content(conversation_id, conversation_data, last_message, token)

    async def _generate_client_confirmation_content(
        self,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate content for client name confirmation message.

        Args:
            conversation_data: The conversation data
            last_message: The last user message

        Returns:
            MessageContent for client confirmation
        """

        conversation: QualConversation = conversation_data.conversation
        conversation_id: UUID = conversation.PublicId  # type: ignore[reportAssignmentType]

        client_handling_response: FieldHandlerResponse = await self.extracted_data_service.single_value_validation(
            field=RequiredField.CLIENT_INFO,
            field_value=conversation_data.confirmed_data.client_name
            or conversation_data.aggregated_data.client_name[0],
            token=token,
        )

        client_handling_reply_type: SystemReplyType | None = client_handling_response.system_reply_type
        if client_handling_reply_type == SystemReplyType.CLIENT_NOT_FOUND:
            reply_type = client_handling_reply_type
            all_fields_extracted_template = ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND
        else:
            reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
            all_fields_extracted_template = ALL_REQUIRED_FIELDS_EXTRACTED_DOCS

        # Get filename from the previous user message
        filenames = await self.document_db_repository.get_filenames_for_message(last_message.id)
        filename = filenames[0] if filenames else None

        if filename:
            filename = filename.split('.')[0]
            formatted_content = all_fields_extracted_template.format(
                client_name=conversation_data.aggregated_data.client_name[0], filename=filename
            )
        else:
            formatted_content = reply_type.message_text.format(
                client_name=conversation_data.aggregated_data.client_name[0]
            )

        suggested_prompts = await self.get_suggested_prompts(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            user_message=cast(UserMessageSerializer, last_message),
            files=None,
            intention=ConversationMessageIntention.EXTRACTION,
            current_reply_type=reply_type,
            called_from=self._generate_client_confirmation_content.__name__,
        )
        return MessageContent(
            formatted_content=formatted_content,
            reply_type=reply_type,
            extracted_options=[],
            suggested_prompts=suggested_prompts,
        )

    async def _generate_standard_system_content(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate standard system message content and options.

        Args:
            conversation_id: The conversation ID
            conversation_data: The conversation data
            last_message: The last user message
            token: The authentication token

        Returns:
            MessageContent for standard system response
        """
        missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
            conversation_id=conversation_id,
            token=token,
            confirmed_data=conversation_data.confirmed_data,
        )
        # Format the extracted data message
        formatted_content, reply_type = self.system_message_service.generate_system_message(
            conversation_data.aggregated_data, conversation_data.confirmed_data, missing_data_response
        )
        extracted_options = self.system_message_service.generate_options(
            conversation_data.aggregated_data,
            conversation_data.confirmed_data,
            ConversationState(conversation_data.conversation.State),
        )

        # Use date options from missing_data_response if available (for date confirmation)
        if not extracted_options and missing_data_response.conversation_state == ConversationState.COLLECTING_DATES:
            extracted_options = (
                self._convert_to_option_objects(missing_data_response.options, missing_data_response.conversation_state)
                if missing_data_response.options
                else [DatePickerOption(start_date=None, end_date=None)]
            )

        if missing_data_response.message and formatted_content != missing_data_response.message:
            formatted_content = missing_data_response.message
            if missing_data_response.reply_type:
                reply_type = missing_data_response.reply_type

        suggested_prompts = await self.get_suggested_prompts(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            user_message=cast(UserMessageSerializer, last_message),
            files=None,
            intention=ConversationMessageIntention.EXTRACTION,
            current_reply_type=reply_type,
            called_from=self._generate_standard_system_content.__name__,
        )

        return MessageContent(
            formatted_content=formatted_content,
            reply_type=reply_type,
            extracted_options=extracted_options,
            suggested_prompts=suggested_prompts,
        )

    async def _clear_last_confirmed_field(self, conversation_id: UUID) -> None:
        """
        Clear the last_confirmed_field to prevent showing confirmation messages repeatedly.

        Args:
            conversation_id: The conversation ID
            token: The authentication token
        """
        try:
            # Get conversation directly and read confirmed data from DB model field
            conversation = await self.conversation_repository.get(conversation_id)
            if not conversation:
                logger.warning(f'Conversation {conversation_id} not found')
                return

            # Parse confirmed data from JSON field
            current_confirmed_data = ConfirmedData.from_json_string(cast(str, conversation.ConfirmedData))
            current_confirmed_data.last_confirmed_field = None

            # Update with cleared field, maintaining current state
            current_state = ConversationState(conversation.State)
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id, confirmed_data=current_confirmed_data, state=current_state
            )

            logger.debug('Successfully cleared last_confirmed_field for conversation ID: %s', conversation_id)
        except Exception as e:
            logger.error('Error clearing last_confirmed_field for conversation %s: %s', conversation_id, e)

    async def _ensure_previous_message_processed(self, conversation_id: UUID) -> None:
        """
        Ensure the previous message has been fully processed before proceeding.

        Args:
            conversation_id: The conversation ID

        Raises:
            ConversationDataInconsistencyError: If the previous message is still being processed
        """
        try:
            last_message = await self.conversation_message_repository.get_last(conversation_id)
        except EntityNotFoundError:
            last_message = None

        if (
            last_message
            and last_message.role == MessageRole.USER
            and last_message.type
            in (
                MessageType.FILE,
                MessageType.TEXT_WITH_FILE,
            )
        ):
            statuses = await self.processing_message_repository.get_message_processing_statuses(last_message.id)
            if ProcessingStatus.DocumentIsCorrupted in statuses:
                message_data = MessageValidator(
                    conversation_id=last_message.conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content='',
                    selected_option=None,
                    files=None,
                    system_reply_type=None,
                )

                await self.conversation_message_repository.create(message_data)

            elif ProcessingStatus.DocumentExtractionCompleted not in statuses:
                raise ConversationDataInconsistencyError(
                    'Cannot create a new message until the previous one has been processed'
                )

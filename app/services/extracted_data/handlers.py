from abc import ABC, abstractmethod
import asyncio
import logging
import re
from typing import Any

from constants.extracted_data import COMPANY_SUFFIXES, FieldStatus, RequiredField
from constants.message import SystemReplyType
from repositories import QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData
from services.ldmf_country import LDMFCountryService


logger = logging.getLogger(__name__)


__all__ = [
    'ClientNameHandler',
    'LDMFCountryHandler',
    'DateIntervalsHandler',
    'ObjectiveHandler',
    'OutcomesHandler',
    'BaseFieldHandler',
    'TokenRequiredFieldHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...

    @abstractmethod
    async def validate_value(self, field_value: Any) -> FieldHandlerResponse:
        """
        Validates a single value for the field.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            field_value: The value to validate

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...


class TokenRequiredFieldHandler(ABC):
    """
    Abstract base class for handlers that require external API authentication.
    This interface is for handlers that need a token for external API calls.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            token: MSAL token from the user for API authentication

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...

    @abstractmethod
    async def validate_value(self, token: str, field_value: Any) -> FieldHandlerResponse:
        """
        Validates a single value for the field.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            token: MSAL token from the user for API authentication
            field_value: The value to validate

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...


class ClientNameHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?\sand\s" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )

    def __init__(self, quals_clients_repository: QualsClientsRepository):
        self.quals_clients_repository = quals_clients_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        # Check if client name is already confirmed
        if confirmed_data.client_name is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        client_names = aggregated_data.client_name
        if client_names:
            client_names = await self._postprocess_client_names(client_names, token)

            if len(client_names) > 1:
                return await self._handle_multiple_client_names(client_names, token)

            if len(client_names) == 1:
                return await self._handle_single_client_name(client_names[0], token)

        # fallback and if client_names is empty
        reply_type = SystemReplyType.NEED_INFO_CLIENT_NAME
        system_message = reply_type.message_text
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=system_message,
            system_reply_type=reply_type,
            next_expected_field=None,
        )

    async def _handle_multiple_client_names(self, client_names, token):
        try:
            search_requests = [
                ClientSearchRequest(contains=client_name, page_size=5, page_idx=0) for client_name in client_names
            ]
            search_results = await asyncio.gather(
                *[self.quals_clients_repository.search_clients(request, token) for request in search_requests],
                return_exceptions=True,
            )
            found_client_names = []
            failed_searches = []
            for i, result in enumerate(search_results):
                if isinstance(result, Exception):
                    failed_searches.append((client_names[i], result))
                else:
                    clients = [client.name for client in result.clients]  # type: ignore
                    found_client_names.extend(clients)
            if failed_searches:
                # Log all failed searches for debugging
                for client_name, error in failed_searches:
                    logger.warning(f"Failed to search for client '{client_name}': {error}")
                raise Exception(f'Failed to search for {len(failed_searches)} client(s)')

            # When we start with multiple client names, always return multiple options
            # regardless of how many unique results the API searches return
            reply_type = SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
            system_message = reply_type.message_text

            # Use found client names if available, otherwise fall back to original client names
            options = found_client_names if found_client_names else client_names

            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.PENDING_CONFIRMATION,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=options,
            )
        except Exception:
            # Fallback: show all client names as options
            reply_type = SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=client_names,
            )

    async def _handle_single_client_name(self, client_name, token):
        try:
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.quals_clients_repository.search_clients(search_request, token)
            if search_result.clients and len(search_result.clients) == 1:
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                system_message = reply_type.message_text.format(client_name=client_name)
                return FieldHandlerResponse(
                    needs_confirmation=False,
                    field_status=FieldStatus.SINGLE,
                    system_message=system_message,
                    system_reply_type=reply_type,
                    next_expected_field=RequiredField.LDMF_COUNTRY,
                    options=[],
                )
            elif search_result.clients and len(search_result.clients) > 1:
                reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
                system_message = reply_type.message_text.format(client_name=client_name)
                return FieldHandlerResponse(
                    needs_confirmation=False,
                    field_status=FieldStatus.MULTIPLE,
                    system_message=system_message,
                    system_reply_type=reply_type,
                    next_expected_field=None,
                    options=[],
                )
            else:
                reply_type = SystemReplyType.CLIENT_NOT_FOUND
                system_message = reply_type.message_text.format(client_name=client_name)
                logger.info('%s system_reply_type detected in %s', reply_type, self._handle_single_client_name.__name__)
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=system_message,
                    system_reply_type=reply_type,
                    next_expected_field=None,
                    options=[],
                )
        except Exception:
            logger.exception('Failed to search for client %s', client_name)
            reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
            system_message = reply_type.message_text.format(client_name=client_name)
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=[],
            )

    async def _postprocess_client_names(self, client_names: list[str], token) -> list[str]:
        result = []

        for client_name in client_names:
            client_name_lower = client_name.lower()
            if 'and' in client_name_lower:
                search_request = ClientSearchRequest(contains=client_name_lower, page_size=5, page_idx=0)
                search_result = await self.quals_clients_repository.search_clients(search_request, token)
                if search_result.clients:
                    result.append(client_name)
                else:
                    client_names = self._split_compound_client_name(client_name)
                    result.extend(client_names)
            else:
                result.append(client_name.removesuffix('.'))

        return result

    @classmethod
    def _split_compound_client_name(cls, client_name: str) -> list[str]:
        split_result = cls.CLIENT_NAME_PATTERN.split(client_name)
        split_result = list(filter(None, split_result))

        current_split = 0
        last_split = len(split_result) - 1
        result = []

        while current_split < last_split:
            company_prefix = split_result[current_split]
            # Check if company suffix is found in the company prefix first word
            if company_prefix.split(maxsplit=1)[0].lower() in COMPANY_SUFFIXES:
                # Extend company prefix with the next split
                current_split += 1
                company_prefix = ''.join([company_prefix, split_result[current_split].removesuffix('.')])
                # Return result if current split is the last
                if current_split == last_split:
                    result.append(company_prefix)
                    return result

            # Define the next split as suffix part
            current_split += 1
            suffix_part = split_result[current_split].split(maxsplit=1)[0].removesuffix('.')
            # Join company prefix and suffix part
            result.append(''.join([company_prefix, suffix_part]))
            # Move to the next split
            current_split += 1

        # Add last split that wasn't processed in the loop
        if current_split == last_split:
            result.append(split_result[-1].removesuffix('.'))

        return result

    async def validate_value(self, token: str, field_value: Any) -> FieldHandlerResponse:
        return await self._handle_single_client_name(field_value, token)


class LDMFCountryHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    def __init__(self, ldmf_country_service: LDMFCountryService):
        self.ldmf_country_service = ldmf_country_service

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        if confirmed_data.ldmf_country:
            return self._create_confirmed_response()

        extracted_countries = aggregated_data.ldmf_country
        if not extracted_countries:
            return self._create_missing_ldmf_country_response()

        if len(extracted_countries) > 1:
            return await self._handle_multiple_ldmf_countries(extracted_countries, token)
        else:  # len(extracted_countries) == 1
            return await self._handle_single_ldmf_country(extracted_countries[0], token)

    def _create_confirmed_response(self) -> FieldHandlerResponse:
        """Creates a response for a confirmed field."""
        return FieldHandlerResponse(
            needs_confirmation=False,
            system_message=None,
            system_reply_type=None,
            next_expected_field=None,
            field_status=FieldStatus.CONFIRMED,
        )

    def _create_missing_ldmf_country_response(self) -> FieldHandlerResponse:
        """Creates a response for a missing LDMF country."""
        reply_type = SystemReplyType.NEED_INFO_LDMF_COUNTRY
        system_message = reply_type.message_text
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=system_message,
            system_reply_type=reply_type,
            next_expected_field=None,
        )

    async def _handle_multiple_ldmf_countries(self, extracted_countries: list[str], token: str) -> FieldHandlerResponse:
        """Handles scenarios where multiple LDMF countries are extracted."""
        verified_countries = []
        for country in extracted_countries:
            verified_country = await self.ldmf_country_service.verify_ldmf_country(country, token)
            if verified_country:
                verified_countries.extend(verified_country)

        if verified_countries:
            reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.PENDING_CONFIRMATION,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=verified_countries,
            )
        else:
            reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=[],
            )

    async def _handle_single_ldmf_country(self, ldmf_country: str, token: str) -> FieldHandlerResponse:
        """Handles scenarios where a single LDMF country is extracted."""
        verified_countries = await self.ldmf_country_service.verify_ldmf_country(ldmf_country, token)
        if verified_countries and len(verified_countries) == 1:
            return self._create_confirmed_response()
        elif verified_countries and len(verified_countries) > 1:
            reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=verified_countries,
            )
        else:
            reply_type = SystemReplyType.EXTRACTED_LDMF_NOT_VALID
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
                options=[ldmf_country],  # Show the original country as an option for user confirmation
            )

    async def validate_value(self, token: str, field_value: Any) -> FieldHandlerResponse:
        return await self._handle_single_ldmf_country(field_value, token)


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if dates are already confirmed (BOTH start and end dates required)
        if confirmed_data.date_intervals:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if dates exist in aggregated data
        if aggregated_data.date_intervals:
            start_date, end_date = aggregated_data.date_intervals[0]
            if start_date or end_date:
                date_info = []
                if start_date:
                    date_info.append(f'Start: {start_date}')
                if end_date:
                    date_info.append(f'End: {end_date}')
                formatted_dates = ' | '.join(date_info)
                reply_type = SystemReplyType.DATES_CONFIRMATION
                system_message = reply_type.message_text.format(dates=formatted_dates)
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.PENDING_CONFIRMATION,
                    system_message=system_message,
                    system_reply_type=reply_type,
                    next_expected_field=None,
                    options=[aggregated_data.date_intervals[0]],
                )

        # No dates found - ask user to provide them
        reply_type = SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING
        system_message = reply_type.message_text
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=system_message,
            system_reply_type=reply_type,
            next_expected_field=RequiredField.ENGAGEMENT_DATES,
        )

    async def validate_value(self, field_value: Any) -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData())


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if objective_and_scope is already confirmed
        if confirmed_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if objective_and_scope is in aggregated data
        elif aggregated_data.objective_and_scope is not None:
            reply_type = SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE
            system_message = reply_type.message_text.format(objective_and_scope=aggregated_data.objective_and_scope)
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            reply_type = SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=RequiredField.OBJECTIVE_SCOPE,
            )

    async def validate_value(self, field_value: Any) -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData())


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            reply_type = SystemReplyType.OUTCOMES_AGGREGATED_QUESTION
            system_message = reply_type.message_text.format(outcomes=aggregated_data.outcomes)
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            reply_type = SystemReplyType.NEED_INFO_OUTCOMES
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )

    async def validate_value(self, field_value: Any) -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData())

from io import By<PERSON><PERSON>
from unittest.mock import patch
from uuid import uuid4

from fastapi import status

from config import settings
from constants.durable_functions import ProcessingStatus
from constants.message import ConversationMessageIntention, MessageRole, MessageType
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from repositories import ConversationMessageRepository
from repositories.document_blob import DocumentBlobRepository
from schemas import MessageValidator, ProcessingStatusUpdatePayload


async def test_upload_documents_success(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = 'Test message with files'

    mocked_intent = ConversationMessageIntention.UNDEFINED

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test files
    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
        ('files', ('test2.pdf', BytesIO(b'Test file content 2'), mime_type)),
    ]

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'content': user_message,
    }

    # Upload documents via message endpoint
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    with patch('services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent):
        response = await async_client.post(
            message_url,
            headers=auth_header,
            data=message_data,
            files=test_files,
        )

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()

    # Verify message was created
    assert data['user']['conversation_id'] == conversation_id
    assert data['user']['content'] == user_message
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.TEXT_WITH_FILE)

    # Verify files were created
    assert data['files'] is not None
    assert len(data['files']) == 2
    assert any(file['file_name'] == 'test1.pdf' for file in data['files'])
    assert any(file['file_name'] == 'test2.pdf' for file in data['files'])


async def test_upload_documents_success_when_blob_repo_is_missing(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    user_message = 'Test message with files'

    mocked_intent = ConversationMessageIntention.UNDEFINED

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test files
    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
        ('files', ('test2.pdf', BytesIO(b'Test file content 2'), mime_type)),
    ]

    # Delete blob repo
    blob_repo = DocumentBlobRepository(settings.document_storage.connection_string)
    async with blob_repo._get_service_client() as service_client:
        container_client = service_client.get_container_client(blob_repo.container_name)
        if await container_client.exists():
            await container_client.delete_container()

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'content': user_message,
    }

    # Upload documents via message endpoint
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    with patch('services.conversation_message.ConversationMessageProcessor._get_intent', return_value=mocked_intent):
        response = await async_client.post(
            message_url,
            headers=auth_header,
            data=message_data,
            files=test_files,
        )

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED


async def test_upload_documents_nonexistent_conversation(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
):
    # Generate a random UUID for a non-existent conversation
    nonexistent_id = str(uuid4())

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test file
    test_files = [
        ('files', ('test.pdf', BytesIO(b'Test file content'), mime_type)),
    ]

    # Prepare message data with non-existent conversation
    message_data = {
        'conversation_id': nonexistent_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with files',
    }

    # Attempt to upload document to non-existent conversation
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert 'detail' in response.json()
    assert 'description' in response.json()


async def test_upload_documents_invalid_file_type(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with invalid file type',
    }

    # Prepare test file with unsupported type
    test_files = [
        ('files', ('test.txt', BytesIO(b'Test file content'), 'text/plain')),
    ]

    # Attempt to upload document with invalid type
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()
    assert 'description' in response.json()

    # Prepare test file with empty content type
    test_files = [
        ('files', ('test.txt', BytesIO(b'Test file content'), '')),
    ]

    # Attempt to upload document with invalid type
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()
    assert 'description' in response.json()


async def test_upload_documents_file_too_large(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
    monkeypatch,
):
    # Mock settings to restrict file size

    monkeypatch.setattr(settings.document_storage, 'max_file_size', 10)  # 10 bytes

    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with file too large',
    }

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test file that's too large
    test_files = [
        ('files', ('test.pdf', BytesIO(b'This content is more than 10 bytes'), mime_type)),
    ]

    # Attempt to upload document that's too large
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()
    assert 'description' in response.json()


async def test_upload_documents_too_many_files(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
    conversation_message_repository_dep_with_autocommit: ConversationMessageRepository,
):
    # First create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'content': 'Test message with too many files',
    }

    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare multiple test files
    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
        ('files', ('test2.pdf', BytesIO(b'Test file content 2'), mime_type)),
    ]

    # Attempt to upload too many documents
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED

    # Create system message that should be created by durable function after extraction.
    await conversation_message_repository_dep_with_autocommit.create(
        MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.FILE,
            content='',
            system_reply_type=None,
            options=[],
            suggested_prompts=[],
        )
    )

    # Prepare multiple test files
    test_files = [
        ('files', ('test3.pdf', BytesIO(b'Test file content 3'), mime_type)),
        ('files', ('test4.pdf', BytesIO(b'Test file content 4'), mime_type)),
    ]

    # Attempt to upload too many documents
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    resp = response.json()
    assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()
    assert 'detail' in resp
    assert resp['detail'].startswith('File upload error [test4.pdf].')
    assert 'description' in resp


async def test_upload_documents_empty_filename(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    # Create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    # Prepare message data
    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with empty filename',
    }

    # Prepare file with empty filename
    mime_type = 'application/pdf'
    test_files = [
        ('files', ('', BytesIO(b'Test file content'), mime_type)),
    ]

    # Attempt to upload document with empty filename
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert 'detail' in response.json()
    assert 'value_error' in str(response.json()['detail']).lower()
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'description' in response.json()

    # Prepare file with only extension
    test_files = [
        ('files', ('.pdf', BytesIO(b'Test file content'), mime_type)),
    ]

    # Attempt to upload document with only extension
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert 'detail' in response.json()
    assert 'value_error' in str(response.json()['detail']).lower()
    assert 'description' in response.json()


async def test_delete_conversation_removes_documents(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that deleting a conversation also removes all associated documents."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    mime_type = 'application/pdf'

    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
    ]

    message_data = {
        'conversation_id': conversation_id,
        'content': 'Test message with files',
    }

    message_url = url_resolver.reverse(operation_ids.message.CREATE)

    new_conversation_data = conversation_data.copy()

    new_message_data = {
        'conversation_id': None,  # Will be filled after creating the second conversation
        'content': 'New message with same filename',
    }
    new_test_files = [
        ('files', ('test1.pdf', BytesIO(b'New file content'), mime_type)),
    ]

    message_response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    data = message_response.json()
    message_id = data['user']['id']

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_header)

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_header)

    message_get_url = url_resolver.reverse(operation_ids.message.GET, message_id=message_id)
    message_get_response = await async_client.get(message_get_url, headers=auth_header)

    new_conversation_response = await async_client.post(
        conversation_url, headers=auth_header, json=new_conversation_data
    )
    new_conversation_id = new_conversation_response.json()['conversation']['id']

    new_message_data['conversation_id'] = new_conversation_id

    create_message_url = url_resolver.reverse(operation_ids.message.CREATE)
    new_message_response = await async_client.post(
        create_message_url,
        headers=auth_header,
        data=new_message_data,
        files=new_test_files,
    )

    assert message_response.status_code == status.HTTP_201_CREATED
    assert data['files'] is not None
    assert len(data['files']) == 1

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT
    assert get_response.status_code == status.HTTP_404_NOT_FOUND
    assert message_get_response.status_code == status.HTTP_404_NOT_FOUND
    assert new_message_response.status_code == status.HTTP_201_CREATED

    new_data = new_message_response.json()
    assert new_data['files'] is not None
    assert len(new_data['files']) == 1
    assert new_data['files'][0]['file_name'] == 'test1.pdf'


async def test_delete_conversation_wrong_doc_blob_path(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that conversation delete succeed even with wrong document blob storage path."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    mime_type = 'application/pdf'

    test_files = [
        ('files', ('test1.pdf', BytesIO(b'Test file content 1'), mime_type)),
    ]

    message_data = {
        'conversation_id': conversation_id,
        'content': 'Test message with files',
    }

    message_url = url_resolver.reverse(operation_ids.message.CREATE)

    message_response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    assert message_response.status_code == status.HTTP_201_CREATED
    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)

    with patch(
        'services.document.DocumentService._DocumentService__generate_blob_path', return_value='not_a_path/at_all'
    ):
        delete_response = await async_client.delete(delete_url, headers=auth_header)
    assert delete_response.status_code == status.HTTP_204_NO_CONTENT


async def test_upload_documents_max_file_size_with_buffer(
    auth_mock,
    auth_header,
    async_client,
    url_resolver,
    conversation_data,
    processing_message_repository_dep_with_autocommit,
    monkeypatch,
):
    """
    Test file upload with max_file_size set to 250MB + 10KB buffer.
    - 250MB + 10KB should pass
    - 250MB + 10KB + 1B should fail
    """
    # Mock settings to restrict file size
    from config import settings

    monkeypatch.setattr(settings.document_storage, 'max_conversation_size', 3050 * 1024 * 1024)  # 3050 MB
    monkeypatch.setattr(settings.document_storage, 'max_docs_per_conversation', 10)  # 10 files

    # Create a conversation
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    message_data = {
        'conversation_id': conversation_id,
        'role': MessageRole.USER,
        'message_type': MessageType.TEXT,
        'content': 'Test message with large file and buffer',
    }
    mime_type = 'application/pdf'
    message_url = url_resolver.reverse(operation_ids.message.CREATE)

    # 250MB + 10KB (should pass)
    file_250mb_10kb = BytesIO(b'a' * (250 * 1024 * 1024 + 10 * 1024))
    response_250mb_10kb = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=[('files', ('250mb_10kb.pdf', file_250mb_10kb, mime_type))],
    )
    assert response_250mb_10kb.status_code == 201

    # Mock DocumentExtractionCompleted status for the previous message
    await processing_message_repository_dep_with_autocommit.create(
        ProcessingStatusUpdatePayload(
            message_id=response_250mb_10kb.json()['user']['id'],
            message=None,
            metadata=None,
            status=ProcessingStatus.DocumentExtractionCompleted,
        )
    )

    # 250MB + 10KB + 1B (should fail)
    file_250mb_10kb_1b = BytesIO(b'a' * (250 * 1024 * 1024 + 10 * 1024 + 1))
    response_250mb_10kb_1b = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=[('files', ('250mb_10kb_1b.pdf', file_250mb_10kb_1b, mime_type))],
    )
    assert response_250mb_10kb_1b.status_code == 400
    assert 'detail' in response_250mb_10kb_1b.json()


async def test_upload_valid_document_after_corrupted(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    conversation_message_repository_dep_with_autocommit,
    processing_message_repository_dep_with_autocommit,
    url_resolver,
    test_conversation_id,
):
    # Use a supported file format (PDF)
    mime_type = 'application/pdf'

    # Prepare test files
    test_files = [('files', ('test.pdf', BytesIO(b'Test file content'), mime_type))]
    content = ''

    # Prepare message data
    message_data = {
        'conversation_id': str(test_conversation_id),
        'content': content,
    }

    # Upload documents via message endpoint
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Mock corrupted status for the message
    await processing_message_repository_dep_with_autocommit.create(
        ProcessingStatusUpdatePayload(
            message_id=response.json()['user']['id'],
            message=None,
            metadata=None,
            status=ProcessingStatus.DocumentIsCorrupted,
        )
    )

    # Upload documents via message endpoint
    response = await async_client.post(
        message_url,
        headers=auth_header,
        data=message_data,
        files=test_files,
    )

    # Verify response
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()

    # Verify message was created
    assert data['user']['conversation_id'] == str(test_conversation_id)
    assert data['user']['content'] == content
    assert data['user']['role'] == str(MessageRole.USER)
    assert data['user']['type'] == str(MessageType.FILE)

    # Verify files were created
    assert data['files'] is not None
    assert len(data['files']) == 1
    assert any(file['file_name'] == 'test.pdf' for file in data['files'])

from unittest.mock import AsyncMock, call

import pytest

from constants.extracted_data import <PERSON><PERSON><PERSON><PERSON>, RequiredField
from constants.message import (
    CLIENT_NAME_MULTIPLE_OPTIONS,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    DATES_CONFIRMATION,
    EXTRACTED_DATA_DATE_INTERVAL_MISSING,
    LDMF_COUNTRY_MULTIPLE_OPTIONS,
    NEED_INFO_CLIENT_NAME,
    NEED_INFO_LDMF_COUNTRY,
    NEED_INFO_OBJECTIVE_SCOPE,
    NEED_INFO_OUTCOMES,
    OUTCOMES_AGGREGATED_QUESTION,
    SystemReplyType,
)
from repositories import QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest
from schemas.confirmed_data import ConfirmedData
from schemas.ldmf_countries import CountryData
from schemas.quals_clients import <PERSON>lientS<PERSON>ch<PERSON><PERSON>, ClientSearchResponse
from services.extracted_data.handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    L<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Object<PERSON><PERSON><PERSON><PERSON>,
    Outcomes<PERSON>andler,
)


@pytest.fixture
def mock_quals_clients_repository():
    return AsyncMock(spec=QualsClientsRepository)


@pytest.fixture
def client_name_handler(mock_quals_clients_repository):
    return ClientNameHandler(quals_clients_repository=mock_quals_clients_repository)


@pytest.fixture
def mock_ldmf_country_service() -> AsyncMock:
    service_mock = AsyncMock()
    service_mock.list.return_value = [
        CountryData(memberFirmId=2470, name='Germany', id=158),
        CountryData(memberFirmId=0, name='Chad', id=141),
        CountryData(memberFirmId=2768, name='United States', id=37),
    ]

    # Mock verify_ldmf_country to return list of country names for known countries, None for unknown
    def mock_verify(country: str, token: str):
        known_countries = ['Germany', 'Chad', 'United States']
        if country in known_countries:
            return [country]
        return None

    service_mock.verify_ldmf_country.side_effect = mock_verify
    return service_mock


@pytest.fixture
def ldmf_country_handler(mock_ldmf_country_service):
    return LDMFCountryHandler(ldmf_country_service=mock_ldmf_country_service)


@pytest.fixture
def outcomes_handler():
    return OutcomesHandler()


@pytest.fixture
def date_intervals_handler():
    return DateIntervalsHandler()


@pytest.fixture
def objective_handler():
    return ObjectiveHandler()


@pytest.mark.asyncio
class TestClientNameHandler:
    async def test_client_name_already_confirmed(self, client_name_handler):
        confirmed_data = ConfirmedData(client_name='Confirmed Client')
        aggregated_data = AggregatedData()
        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_multiple_client_names_in_aggregated_data_found_in_api(
        self, client_name_handler, mock_quals_clients_repository
    ):
        aggregated_data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return a single client for each search request
        mock_quals_clients_repository.search_clients.side_effect = [
            ClientSearchResponse(
                clients=[ClientSearchItem(id=1, name='Client A', qualsCount=1, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
            ClientSearchResponse(
                clients=[ClientSearchItem(id=2, name='Client B', qualsCount=1, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
        ]

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A', 'Client B']
        mock_quals_clients_repository.search_clients.assert_has_calls(
            [
                call(ClientSearchRequest(contains='Client A', page_size=5, page_idx=0), 'test_token'),
                call(ClientSearchRequest(contains='Client B', page_size=5, page_idx=0), 'test_token'),
            ],
            any_order=True,
        )

    async def test_multiple_client_names_in_aggregated_data_single_match_from_api(
        self, client_name_handler, mock_quals_clients_repository
    ):
        """Test that multiple client names always return multiple options, even if API finds only one match."""
        aggregated_data = AggregatedData(client_name=['Client A', 'Client C'])
        confirmed_data = ConfirmedData()

        mock_quals_clients_repository.search_clients.side_effect = [
            ClientSearchResponse(
                clients=[ClientSearchItem(id=1, name='Client A', qualsCount=1, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
            ClientSearchResponse(clients=[], total_count=0, page_size=5, page_idx=0),
        ]

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_reply_type == SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A']  # Only the found client name
        mock_quals_clients_repository.search_clients.assert_has_calls(
            [
                call(ClientSearchRequest(contains='Client A', page_size=5, page_idx=0), 'test_token'),
                call(ClientSearchRequest(contains='Client C', page_size=5, page_idx=0), 'test_token'),
            ],
            any_order=True,
        )

    async def test_multiple_client_names_in_aggregated_data_api_fails(
        self, client_name_handler, mock_quals_clients_repository
    ):
        aggregated_data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to raise an exception
        mock_quals_clients_repository.search_clients.side_effect = Exception('API Error')

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A', 'Client B']

    async def test_single_client_name_api_multiple_matches(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Single Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return multiple clients
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[
                ClientSearchItem(id=3, name='Single Client 1', qualsCount=1, clientConfidentiality=1),
                ClientSearchItem(id=4, name='Single Client 2', qualsCount=1, clientConfidentiality=1),
            ],
            total_count=2,
            page_size=5,
            page_idx=0,
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Single Client')
        assert response.options == []
        mock_quals_clients_repository.search_clients.assert_called_once_with(
            ClientSearchRequest(contains='Single Client', page_size=5, page_idx=0), 'test_token'
        )

    async def test_single_client_name_api_no_matches(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['NonExistent Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return no clients
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[], total_count=0, page_size=5, page_idx=0
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == CLIENT_NOT_FOUND_PROMPT.format(client_name='NonExistent Client')
        assert response.options == []

    async def test_single_client_name_api_exact_match(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Exact Match Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return exactly one client
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[ClientSearchItem(id=5, name='Exact Match Client', qualsCount=1, clientConfidentiality=1)],
            total_count=1,
            page_size=5,
            page_idx=0,
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        # we should ask confirmation
        assert not response.needs_confirmation
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message is not None
        assert response.options == []

    async def test_single_client_name_api_fails(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Client With Error'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to raise an exception
        mock_quals_clients_repository.search_clients.side_effect = Exception('API Error')

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Client With Error')
        assert response.options == []

    async def test_no_client_names_in_aggregated_data(self, client_name_handler):
        aggregated_data = AggregatedData(client_name=[])
        confirmed_data = ConfirmedData()
        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_CLIENT_NAME
        assert response.next_expected_field is None

    async def test_client_name_no_contains_and(self, client_name_handler):
        client_name = 'Company Ltd.'
        confirmed_data = ConfirmedData()

        postprocess_result = await client_name_handler._postprocess_client_names([client_name], confirmed_data)
        assert postprocess_result == ['Company Ltd']

    async def test_client_name_contains_and_api_no_matches(self, client_name_handler, mock_quals_clients_repository):
        client_name = 'A Inc. and B LLC'
        confirmed_data = ConfirmedData()

        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[], total_count=0, page_size=5, page_idx=0
        )

        postprocess_result = await client_name_handler._postprocess_client_names([client_name], confirmed_data)
        assert postprocess_result == ['A Inc', 'B LLC']

    @pytest.mark.parametrize(
        'parametrized_data',
        [
            {
                'initial_client_name': 'Co and care Ltd.',
                'result_client_names': ['Co and care Ltd'],
            },
            {
                'initial_client_name': 'Nordea Finance Finland Ltd and Green Innovation LLC',
                'result_client_names': ['Nordea Finance Finland Ltd', 'Green Innovation LLC'],
            },
            {
                'initial_client_name': 'Apple Inc. and CO AND CO ORGANICS LIMITED',
                'result_client_names': ['Apple Inc', 'CO AND CO ORGANICS LIMITED'],
            },
            {
                'initial_client_name': 'CO AND NUTS RISK SOLUTIONS LIMITED and Virgin Atlantic Airways Ltd.',
                'result_client_names': ['CO AND NUTS RISK SOLUTIONS LIMITED', 'Virgin Atlantic Airways Ltd'],
            },
            {
                'initial_client_name': 'Virgin Atlantic Airways Ltd. and CO AND NUTS RISK SOLUTIONS LIMITED',
                'result_client_names': ['Virgin Atlantic Airways Ltd', 'CO AND NUTS RISK SOLUTIONS LIMITED'],
            },
        ],
    )
    async def test_client_name_contains_and_api_no_matches_company_suffix_in_name(
        self, parametrized_data, client_name_handler, mock_quals_clients_repository
    ):
        client_name = parametrized_data['initial_client_name']
        confirmed_data = ConfirmedData()

        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[], total_count=0, page_size=5, page_idx=0
        )

        postprocess_result = await client_name_handler._postprocess_client_names([client_name], confirmed_data)
        assert postprocess_result == parametrized_data['result_client_names']

    async def test_client_name_contains_and_api_exact_match(self, client_name_handler, mock_quals_clients_repository):
        client_name = 'Marshmallow and Mellow'
        confirmed_data = ConfirmedData()

        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[ClientSearchItem(id=5, name=client_name, qualsCount=1, clientConfidentiality=1)],
            total_count=1,
            page_size=5,
            page_idx=0,
        )

        postprocess_data = await client_name_handler._postprocess_client_names([client_name], confirmed_data)
        assert postprocess_data == [client_name]

    async def test_multiple_client_names_always_returns_multiple_options_regardless_of_api_results(
        self, client_name_handler, mock_quals_clients_repository
    ):
        """
        Test that demonstrates the bug fix: when aggregated data contains multiple client names,
        the system should always return CLIENT_NAME_MULTIPLE_OPTIONS regardless of API search results.
        This ensures consistent behavior across all environments.
        """
        # Test case: Multiple client names, API returns only one unique result (the bug scenario)
        aggregated_data = AggregatedData(client_name=['Deloitte US', 'Deloitte UK'])
        confirmed_data = ConfirmedData()

        mock_quals_clients_repository.search_clients.side_effect = [
            ClientSearchResponse(
                clients=[ClientSearchItem(id=1, name='Deloitte', qualsCount=10, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
            ClientSearchResponse(
                clients=[ClientSearchItem(id=1, name='Deloitte', qualsCount=10, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
        ]

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')
        # This should return MULTIPLE_OPTIONS even though API returned only one unique result
        assert response.needs_confirmation
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_reply_type == SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Deloitte', 'Deloitte']  # Duplicates from API results


@pytest.mark.asyncio
class TestLDMFCountryHandler:
    async def test_ldmf_country_handler_returns_missing(self, ldmf_country_handler):
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_ldmf_country_already_confirmed(self, ldmf_country_handler):
        confirmed_data = ConfirmedData(ldmf_country='Germany')
        aggregated_data = AggregatedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_ldmf_country_fetch_exception(self, ldmf_country_handler, mock_ldmf_country_service):
        """Test handling of exception during LDMF country fetch."""
        mock_ldmf_country_service.verify_ldmf_country.side_effect = Exception('API Error')
        aggregated_data = AggregatedData(ldmf_country=['Germany'])
        confirmed_data = ConfirmedData()

        # The current implementation doesn't catch exceptions, so it should propagate
        with pytest.raises(Exception, match='API Error'):
            await ldmf_country_handler.check_and_get_response(aggregated_data, confirmed_data, token='test_token')

    async def test_single_valid_country(self, ldmf_country_handler):
        """Test handling of a single valid country."""
        aggregated_data = AggregatedData(ldmf_country=['Germany'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_single_invalid_country(self, ldmf_country_handler):
        """Test handling of a single invalid country."""
        aggregated_data = AggregatedData(ldmf_country=['InvalidCountry'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
        assert response.next_expected_field is None
        assert response.options == ['InvalidCountry']

    async def test_single_country_with_multiple_matches(self, ldmf_country_handler, mock_ldmf_country_service):
        """Test handling of a single country that returns multiple verification matches."""
        # Mock verify_ldmf_country to return multiple countries for a single input
        mock_verify_multiple = lambda *args, **kwargs: ['United States', 'United States Minor Outlying Islands']
        mock_ldmf_country_service.verify_ldmf_country.side_effect = mock_verify_multiple

        aggregated_data = AggregatedData(ldmf_country=['US'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == LDMF_COUNTRY_MULTIPLE_OPTIONS
        assert response.next_expected_field is None
        assert sorted(response.options) == sorted(['United States', 'United States Minor Outlying Islands'])

    async def test_single_country_with_empty_verification(self, ldmf_country_handler, mock_ldmf_country_service):
        """Test handling of a single country that returns empty list from verification."""
        # Mock verify_ldmf_country to return empty list for a single input
        mock_verify_empty = lambda *args, **kwargs: []
        mock_ldmf_country_service.verify_ldmf_country.side_effect = mock_verify_empty

        aggregated_data = AggregatedData(ldmf_country=['UnknownCountry'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
        assert response.next_expected_field is None
        assert response.options == ['UnknownCountry']

    async def test_multiple_valid_countries(self, ldmf_country_handler):
        """Test handling of multiple valid countries."""
        aggregated_data = AggregatedData(ldmf_country=['Germany', 'United States'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.system_message == LDMF_COUNTRY_MULTIPLE_OPTIONS
        assert response.next_expected_field is None
        assert sorted(response.options) == sorted(['Germany', 'United States'])

    async def test_multiple_mixed_countries(self, ldmf_country_handler):
        """Test handling of mixed valid and invalid countries."""
        aggregated_data = AggregatedData(ldmf_country=['Germany', 'InvalidCountry', 'United States'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.system_message == LDMF_COUNTRY_MULTIPLE_OPTIONS
        assert response.next_expected_field is None
        assert sorted(response.options) == sorted(['Germany', 'United States'])

    async def test_multiple_invalid_countries(self, ldmf_country_handler):
        """Test handling of multiple invalid countries."""
        aggregated_data = AggregatedData(ldmf_country=['InvalidCountry1', 'InvalidCountry2'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == LDMF_COUNTRY_MULTIPLE_OPTIONS
        assert response.next_expected_field is None
        # Invalid countries are filtered out, so options should be empty
        assert response.options == []


@pytest.mark.asyncio
class TestOutcomesHandler:
    async def test_outcomes_already_confirmed(self, outcomes_handler):
        confirmed_data = ConfirmedData(outcomes='Confirmed Outcome')
        aggregated_data = AggregatedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_outcomes_in_aggregated_data(self, outcomes_handler):
        aggregated_data = AggregatedData(outcomes='Aggregated Outcome')
        confirmed_data = ConfirmedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == OUTCOMES_AGGREGATED_QUESTION.format(outcomes=aggregated_data.outcomes)
        assert response.next_expected_field is None

    async def test_outcomes_missing(self, outcomes_handler):
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_OUTCOMES
        assert response.next_expected_field is None


@pytest.mark.asyncio
class TestDateIntervalsHandler:
    async def test_date_intervals_already_confirmed(self, date_intervals_handler):
        confirmed_data = ConfirmedData(date_intervals=('2023-01-01', '2023-01-31'))
        aggregated_data = AggregatedData()
        response = await date_intervals_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_date_intervals_in_aggregated_data_with_both_dates(self, date_intervals_handler):
        aggregated_data = AggregatedData(date_intervals=[('2023-01-01', '2023-01-31')])
        confirmed_data = ConfirmedData()
        response = await date_intervals_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == DATES_CONFIRMATION.format(dates='Start: 2023-01-01 | End: 2023-01-31')
        assert response.next_expected_field is None
        assert response.options == [('2023-01-01', '2023-01-31')]

    async def test_date_intervals_in_aggregated_data_with_start_date_only(self, date_intervals_handler):
        aggregated_data = AggregatedData(date_intervals=[('2023-01-01', None)])
        confirmed_data = ConfirmedData()
        response = await date_intervals_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == DATES_CONFIRMATION.format(dates='Start: 2023-01-01')
        assert response.next_expected_field is None
        assert response.options == [('2023-01-01', None)]

    async def test_date_intervals_in_aggregated_data_with_end_date_only(self, date_intervals_handler):
        aggregated_data = AggregatedData(date_intervals=[(None, '2023-01-31')])
        confirmed_data = ConfirmedData()
        response = await date_intervals_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == DATES_CONFIRMATION.format(dates='End: 2023-01-31')
        assert response.next_expected_field is None
        assert response.options == [(None, '2023-01-31')]

    async def test_no_date_intervals_in_aggregated_data(self, date_intervals_handler):
        aggregated_data = AggregatedData(date_intervals=[])
        confirmed_data = ConfirmedData()
        response = await date_intervals_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == EXTRACTED_DATA_DATE_INTERVAL_MISSING
        assert response.next_expected_field == RequiredField.ENGAGEMENT_DATES


@pytest.mark.asyncio
class TestObjectiveHandler:
    async def test_objective_already_confirmed(self, objective_handler):
        confirmed_data = ConfirmedData(objective_and_scope='Confirmed Objective')
        aggregated_data = AggregatedData()
        response = await objective_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_objective_in_aggregated_data(self, objective_handler):
        aggregated_data = AggregatedData(objective_and_scope='Aggregated Objective')
        confirmed_data = ConfirmedData()
        response = await objective_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert (
            response.system_message
            == f'Found this objective and scope: {aggregated_data.objective_and_scope}. Can you confirm?'
        )
        assert response.next_expected_field is None

    async def test_objective_missing(self, objective_handler):
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        response = await objective_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_OBJECTIVE_SCOPE
        assert response.next_expected_field == RequiredField.OBJECTIVE_SCOPE

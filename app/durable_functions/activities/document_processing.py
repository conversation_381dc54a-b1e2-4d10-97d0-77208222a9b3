from datetime import datetime, timezone
import json
import logging
import os
from typing import Any, Dict, cast
from uuid import UUID

from azure.core.exceptions import HttpResponseError
import azure.durable_functions as df
from openai import AsyncAzureOpenAI
from sqlalchemy.ext.asyncio import AsyncSession

from constants.durable_functions import ActivityName, ExctractStatus
from durable_functions.application.config import settings
from durable_functions.application.db import async_session_local
from durable_functions.repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentQueueRepository,
    ExtractedDataRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
)
from durable_functions.utils import (
    <PERSON><PERSON>bS<PERSON>ageHelper,
    DocumentIntelligenceHelper,
    ExtractedDataMerger,
    RecursiveChunkingStrategy,
    SignalRApiClient,
    activity_logging_decorator,
)
from durable_functions.utils.models import EngagementPeriod, FinalExtractionDataResults, LLMExtractedDataResult
from schemas import ExtractedData, ProcessingStatusUpdatePayload

from .models import (
    AggregateMultiSourceDataActivityInput,
    ChunkDocumentActivityInput,
    ChunkDocumentActivityOutput,
    ExtractDataActivityInput,
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    ReadPromptActivityInput,
    SaveAggregatedResultsToBlobActivityInput,
    SaveExtractionDataActivityInput,
    SendFinalQueueMessageActivityInput,
    SendNotificationActivityInput,
    SendQueueMessageActivityInput,
    UpdateProcessingStatusActivityInput,
)


logger = logging.getLogger(__name__)


bp = df.Blueprint()


@bp.activity_trigger('document', ActivityName.ExtractDocumentText)
@activity_logging_decorator
def extract_document_text(
    document: ExtractDocumentTextActivityInput,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """
    Activity function to extract text from a document using Document Intelligence.
    """
    try:
        file_name = document.file_name
        logger.info(f'Extracting text from document {file_name} for message {document.message_id}')

        blob_helper = BlobStorageHelper()
        document_bytes = blob_helper.get_blob_from_url(document.blob_url)

        try:
            doc_intelligence = DocumentIntelligenceHelper()
            extraction_result = doc_intelligence.extract_text_from_document(document_bytes)
        except HttpResponseError as e:
            error_message = f'Document extraction failed: {str(e)}'
            is_corrupted = False
            if 'The file is corrupted or format is unsupported.' in e.message:
                error_message = 'The file is corrupted or format is unsupported.'
                is_corrupted = True
                logger.error(f'Document Intelligence extraction failed: {error_message}')

            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=error_message,
                status=ExctractStatus.Failed,
                file_is_corrupted=is_corrupted,
            )

        except Exception as e:
            logger.error(f'Document Intelligence extraction failed: {str(e)}')
            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=f'Document extraction failed: {str(e)}',
                status=ExctractStatus.Failed,
            )

        base_name = os.path.splitext(file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, extraction_result)

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=file_name,
            extraction_url=extraction_url,
            text_content=extraction_result['text'],
            metadata={
                **extraction_result['metadata'],
                'original_blob_url': document.blob_url,
            },
            status=ExctractStatus.Success,
        )

    except Exception:
        logger.exception('Error extracting document text')
        raise


@bp.activity_trigger('updates', ActivityName.UpdateProcessingStatus)
@activity_logging_decorator
async def update_processing_status(updates: UpdateProcessingStatusActivityInput) -> Dict[str, Any]:
    """
    Activity function to update the file processing status.

    This function demonstrates how to integrate with FastAPI repositories.
    Currently logs the status update, but can be enhanced to use database repositories.
    """
    try:
        logger.info(f'Updating processing status for message_id {updates.message_id}: {updates.status}')

        async with cast(AsyncSession, async_session_local()) as session:
            process_message_repo = ProcessingMessageRepository(session)

            # Update message processing status in database
            await process_message_repo.create(
                data=ProcessingStatusUpdatePayload(
                    message_id=UUID(updates.message_id),
                    status=updates.status,
                    message=updates.message,
                    metadata=updates.metadata,
                )
            )
            await session.commit()

        logger.info(f'Processing status update: {updates.status} - {updates.message}')
        if updates.metadata:
            logger.info(f'Metadata: {updates.metadata}')

        return {
            'status': 'success',
            'message_id': updates.message_id,
            'processing_status': updates.status,
            'message': updates.message,
            'metadata': updates.metadata,
        }

    except Exception:
        logger.exception('Error updating processing status')
        raise


@bp.activity_trigger('notification', ActivityName.SendNotification)
@activity_logging_decorator
async def send_notification(notification: SendNotificationActivityInput) -> None:
    """
    Activity function to send a notification via SignalR.

    Args:
        notification: Notification data including event type, data, and optional signalr_user_id

    Returns:
        None
    """
    try:
        logger.info(f'Sending notification event: {notification.event_type}')
        signalr_client = SignalRApiClient()

        # Pass the signalr_user_id to the send_notification method
        await signalr_client.send_notification(
            event_type=notification.event_type, data=notification.data, user_id=notification.signalr_user_id
        )

    except Exception:
        logger.exception('Error sending notification')
        raise


@bp.activity_trigger('extraction', ActivityName.ChunkDocument)
@activity_logging_decorator
def chunk_document(extraction: ChunkDocumentActivityInput) -> ChunkDocumentActivityOutput:
    """
    Activity function to chunk a document.
    """
    try:
        message_id = extraction.message_id
        file_name = extraction.file_name
        text_content = extraction.text_content
        metadata = extraction.metadata

        logger.info(f'Chunking document {file_name} for message {message_id}')

        chunking_strategy = RecursiveChunkingStrategy()
        document_metadata = {
            'message_id': message_id,
            'file_name': file_name,
        }
        for key, value in metadata.items():
            document_metadata[key] = value

        chunks = chunking_strategy.chunk_document(text_content, document_metadata)

        blob_helper = BlobStorageHelper()
        chunk_urls = []

        for chunk in chunks:
            base_name = os.path.splitext(file_name)[0]  # type: ignore
            chunk_filename = f'{base_name}_chunk_{chunk["chunk_index"]}.json'
            chunk_path = f'chunks/{message_id}/{chunk_filename}'
            chunk_url = blob_helper.upload_json(chunk_path, chunk)
            chunk_urls.append({'chunk_index': chunk['chunk_index'], 'chunk_id': chunk['chunk_id'], 'url': chunk_url})

        return ChunkDocumentActivityOutput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=len(chunks),
            chunk_urls=chunk_urls,
        )

    except Exception:
        logger.exception('Error chunking document')
        raise


@bp.activity_trigger('message', ActivityName.SendQueueMessage)
@activity_logging_decorator
async def send_queue_message(message: SendQueueMessageActivityInput) -> None:
    queue_name = settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_CHUNKED

    try:
        logger.info(f'Sending chunk processing message for message_id: {message.message_id}')

        queue_repo = DocumentQueueRepository(
            connection_string=settings.QUEUE_SETTINGS.CONNECTION_STRING,
            queue_name=queue_name,
        )

        # Create a smaller message payload that references the chunks
        # instead of including all their URLs, to avoid exceeding the queue message size limit.
        message_data = {
            'message_id': message.message_id,
            'file_name': message.file_name,
            'chunk_count': message.chunk_count,
            'signalr_user_id': message.signalr_user_id,
            'input_type': message.input_type.value,
        }

        # Use the public send_message to send a raw dictionary,
        # assuming the consumer of this queue can handle this format.
        await queue_repo.send_message(message_data)

    except Exception as e:
        logger.error(f'Error sending queue message: {str(e)}')
        raise


@bp.activity_trigger('prompt', ActivityName.ReadPrompt)
@activity_logging_decorator
async def read_prompt(prompt: ReadPromptActivityInput) -> str:
    try:
        prompt_url = prompt.prompt_url
        logger.info(f'Reading prompt from {prompt_url}')
        blob_helper = BlobStorageHelper()
        prompt_text = blob_helper.get_blob_from_url(prompt_url).decode('utf-8')
        return prompt_text
    except Exception:
        logger.exception('Error reading prompt')
        raise


@bp.activity_trigger('extraction', ActivityName.ExtractData)
@activity_logging_decorator
async def extract_data(extraction: ExtractDataActivityInput) -> LLMExtractedDataResult:
    """
    Activity function to extract data from a document.
    """
    blob_helper = BlobStorageHelper()

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)

        try:
            if extraction.chunk_url:
                chunk_url = extraction.chunk_url
                # Use get_blob_from_url which handles full URLs and extracts the path correctly
                chunk_data_bytes = blob_helper.get_blob_from_url(str(chunk_url))
                chunk_data = json.loads(chunk_data_bytes.decode('utf-8'))
                text = chunk_data.get('text')
            elif extraction.text_content:
                text = extraction.text_content
            else:
                raise ValueError('No chunk_url or text_content provided')

            if text and (stripped_text := text.strip()):
                logger.info('Extracting data from text')
                return await openai_repo.extract_data(stripped_text)
            else:
                return LLMExtractedDataResult()
        except Exception:
            logger.exception('Error extracting data')
            raise


@bp.activity_trigger('merging', ActivityName.MergeExtractionData)
@activity_logging_decorator
async def merge_extraction_data(merging) -> FinalExtractionDataResults:
    try:
        return ExtractedDataMerger.merge_results(merging)
    except Exception:
        logger.exception('Error merging extraction data')
        raise


@bp.activity_trigger('saving', ActivityName.SaveExtractionData)
@activity_logging_decorator
async def save_extraction_data(saving: SaveExtractionDataActivityInput):
    """
    Activity function to save the extraction data to the database.
    """
    logger.info(f'Saving extraction data: {saving}')
    try:
        async with cast(AsyncSession, async_session_local()) as session:
            extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
            message_repo = ConversationMessageRepository(session, ConversationRepository(session))

            message_id = UUID(saving.message_id)
            logger.info(f'Message ID: {message_id}')
            message = await message_repo.get(message_id)
            if not message:
                logger.warning(f'Message not found for message_id: {message_id}')
                raise ValueError(f'Message not found for message_id: {message_id}')

            client_name = (
                json.dumps(saving.extraction_data.client_names) if saving.extraction_data.client_names else '[]'
            )
            ldmf_country = (
                json.dumps(saving.extraction_data.lead_member_countries)
                if saving.extraction_data.lead_member_countries
                else '[]'
            )
            if saving.extraction_data.periods:
                # get first period
                start_date = saving.extraction_data.periods[0].start_date
                end_date = saving.extraction_data.periods[0].end_date
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
            else:
                start_date = None
                end_date = None

            if saving.extraction_data.periods_original:
                start_date_original = saving.extraction_data.periods_original[0].start_date
                end_date_original = saving.extraction_data.periods_original[0].end_date
            else:
                start_date_original = None
                end_date_original = None

            extracted_data = ExtractedData(
                ConversationPublicId=message.conversation_id,
                DataSourceType=saving.data_source_type,
                ClientName=client_name,
                LDMFCountry=ldmf_country,
                StartDate=start_date,
                EndDate=end_date,
                StartDateOriginal=start_date_original,
                EndDateOriginal=end_date_original,
                ObjectiveAndScope=saving.extraction_data.objective_and_scope,
                Outcomes=saving.extraction_data.outcomes,
                CreatedAt=datetime.now(timezone.utc),
            )
            await extracted_data_repo.update(extracted_data)
            await session.commit()

    except Exception:
        logger.exception('Error saving extraction data')
        raise


@bp.activity_trigger('aggregation', ActivityName.AggregateMultiSourceData)
@activity_logging_decorator
async def aggregate_multi_source_data(aggregation: AggregateMultiSourceDataActivityInput) -> FinalExtractionDataResults:
    """
    Activity function to aggregate extraction data from multiple sources.
    """
    logger.info(f'Aggregating multi-source data: {aggregation}')
    try:
        async with cast(AsyncSession, async_session_local()) as session:
            extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
            message_repo = ConversationMessageRepository(session, ConversationRepository(session))

            # Get message to find conversation_id
            message = await message_repo.get(UUID(aggregation.message_id))
            if not message:
                raise ValueError(f'Message not found: {aggregation.message_id}')

            # Retrieve extraction results from all sources
            source_results = []
            for source_type, _ in aggregation.source_results:
                extracted_data = await extracted_data_repo.get(
                    conversation_id=message.conversation_id, data_source_type=source_type
                )
                if extracted_data:
                    # Convert to FinalExtractionDataResults format
                    periods = None
                    if extracted_data.start_date or extracted_data.end_date:
                        periods = [
                            EngagementPeriod(
                                start_date=extracted_data.start_date.isoformat() if extracted_data.start_date else None,
                                end_date=extracted_data.end_date.isoformat() if extracted_data.end_date else None,
                            )
                        ]

                    # get original periods
                    periods_original = None
                    if extracted_data.start_date_original or extracted_data.end_date_original:
                        periods_original = [
                            EngagementPeriod(
                                start_date=extracted_data.start_date_original
                                if extracted_data.start_date_original
                                else None,
                                end_date=extracted_data.end_date_original if extracted_data.end_date_original else None,
                            )
                        ]

                    final_result = FinalExtractionDataResults(
                        client_names=extracted_data.client_name if isinstance(extracted_data.client_name, list) else [],
                        lead_member_countries=extracted_data.ldmf_country
                        if isinstance(extracted_data.ldmf_country, list)
                        else [],
                        periods=periods,
                        periods_original=periods_original,
                        objective_and_scope=extracted_data.objective_and_scope,
                        outcomes=extracted_data.outcomes,
                    )
                    source_results.append((source_type, final_result))

            # Aggregate results using the enhanced merger
            aggregated_results = ExtractedDataMerger.merge_multi_source_results(source_results)

            logger.info(f'Aggregated results: {aggregated_results}')

            await session.commit()
            return aggregated_results

    except Exception:
        logger.exception('Error aggregating multi-source data')
        raise


@bp.activity_trigger('blob_save', ActivityName.SaveAggregatedResultsToBlob)
@activity_logging_decorator
async def save_aggregated_results_to_blob(blob_save: SaveAggregatedResultsToBlobActivityInput) -> str:
    """
    Activity function to save aggregated results to blob storage.
    """
    logger.info(f'Saving aggregated results to blob: {blob_save}')
    try:
        blob_helper = BlobStorageHelper()

        # Create blob path for aggregated results
        blob_path = f'aggregated/{blob_save.message_id}/final_results.json'

        # Save aggregated data to blob storage
        blob_url = blob_helper.upload_json(blob_path, blob_save.aggregated_data.model_dump())

        logger.info(f'Saved aggregated results to blob: {blob_url}')
        return blob_url

    except Exception:
        logger.exception('Error saving aggregated results to blob')
        raise


@bp.activity_trigger('final_queue', ActivityName.SendFinalQueueMessage)
@activity_logging_decorator
async def send_final_queue_message(final_queue: SendFinalQueueMessageActivityInput):
    """
    Activity function to send final queue message for further processing.
    """
    logger.info(f'Sending final queue message: {final_queue}')
    try:
        # Send message to the extracted queue for further processing
        queue_repo = DocumentQueueRepository(
            settings.QUEUE_SETTINGS.CONNECTION_STRING, settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_EXTRACTED
        )

        message_data = {
            'message_id': final_queue.message_id,
            'blob_url': final_queue.blob_url,
            'signalr_user_id': final_queue.signalr_user_id,
            'processing_type': 'unified_aggregated',
        }

        await queue_repo._send_message(message_data)

        logger.info(f'Sent final queue message for message_id: {final_queue.message_id}')

    except Exception:
        logger.exception('Error sending final queue message')
        raise
